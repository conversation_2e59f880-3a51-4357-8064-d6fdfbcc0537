<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Button Test - AudienceHost</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .btn {
            display: inline-block;
            padding: 12px 24px;
            margin: 10px;
            text-decoration: none;
            border-radius: 5px;
            font-weight: bold;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .btn--primary {
            background: #007bff;
            color: white;
        }
        .btn--outline {
            background: transparent;
            color: #007bff;
            border: 2px solid #007bff;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
        .pricing__card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            margin: 20px;
            text-align: center;
        }
        .log {
            background: #000;
            color: #0f0;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            max-height: 300px;
            overflow-y: auto;
            margin: 10px 0;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .status.success { background: #d4edda; color: #155724; }
        .status.error { background: #f8d7da; color: #721c24; }
        .status.info { background: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <h1>🔗 Checkout Button Test</h1>
    
    <div class="test-section">
        <h2>Current Status</h2>
        <div id="status-display" class="status info">Testing buttons...</div>
    </div>

    <div class="test-section">
        <h2>Test Buttons (Same as Main Website)</h2>
        
        <!-- Single Plan -->
        <div class="pricing__card">
            <h3>Single Plan</h3>
            <div class="pricing__actions">
                <a href="#" class="btn btn--outline btn--full checkout-btn" data-plan="single" data-currency="USD">
                    Get Started (USD)
                </a>
                <a href="#" class="btn btn--outline btn--full checkout-btn" data-plan="single" data-currency="PKR" style="display: none;">
                    Get Started (PKR)
                </a>
            </div>
        </div>

        <!-- Business Plan -->
        <div class="pricing__card">
            <h3>Business Plan</h3>
            <div class="pricing__actions">
                <a href="#" class="btn btn--primary btn--full checkout-btn" data-plan="business" data-currency="USD">
                    Get Started (USD)
                </a>
                <a href="#" class="btn btn--primary btn--full checkout-btn" data-plan="business" data-currency="PKR" style="display: none;">
                    Get Started (PKR)
                </a>
            </div>
        </div>

        <!-- Reseller Plan -->
        <div class="pricing__card">
            <h3>Reseller Plan</h3>
            <div class="pricing__actions">
                <a href="#" class="btn btn--outline btn--full checkout-btn" data-plan="reseller" data-currency="USD">
                    Get Started (USD)
                </a>
                <a href="#" class="btn btn--outline btn--full checkout-btn" data-plan="reseller" data-currency="PKR" style="display: none;">
                    Get Started (PKR)
                </a>
            </div>
        </div>
    </div>

    <div class="test-section">
        <h2>Manual Tests</h2>
        <button onclick="testPakistaniPricing()" class="btn btn--primary">Force Pakistani Pricing</button>
        <button onclick="testGlobalPricing()" class="btn btn--outline">Force Global Pricing</button>
        <button onclick="checkButtonHrefs()" class="btn btn--outline">Check Button URLs</button>
        <button onclick="clearLog()" class="btn btn--outline">Clear Log</button>
    </div>

    <div class="test-section">
        <h2>Debug Log</h2>
        <div id="debug-log" class="log"></div>
    </div>

    <script>
        let userCountry = 'US';
        let userCurrency = 'USD';
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logDiv = document.getElementById('debug-log');
            const color = type === 'error' ? '#ff6b6b' : type === 'success' ? '#51cf66' : '#74c0fc';
            logDiv.innerHTML += `<div style="color: ${color}">[${timestamp}] ${message}</div>`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(`[${timestamp}] ${message}`);
        }

        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status-display');
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
        }

        function checkButtonHrefs() {
            log('Checking all button URLs...', 'info');
            const buttons = document.querySelectorAll('.checkout-btn');
            
            buttons.forEach((button, index) => {
                const plan = button.getAttribute('data-plan');
                const currency = button.getAttribute('data-currency');
                const href = button.href;
                const visible = button.style.display !== 'none';
                
                log(`Button ${index + 1}: ${plan} (${currency}) - ${visible ? 'VISIBLE' : 'HIDDEN'} - URL: ${href}`, visible ? 'success' : 'info');
            });
        }

        function setPakistaniPricing() {
            log('Setting Pakistani pricing...', 'info');

            // Update checkout buttons
            const checkoutButtons = document.querySelectorAll('.checkout-btn');
            log(`Found ${checkoutButtons.length} checkout buttons`, 'info');
            
            checkoutButtons.forEach((button, index) => {
                const plan = button.getAttribute('data-plan');
                const currency = button.getAttribute('data-currency');
                log(`Button ${index}: plan=${plan}, currency=${currency}`, 'info');

                if (currency === 'PKR') {
                    button.style.display = 'inline-block';
                    // Set appropriate PKR checkout URLs
                    if (plan === 'single') {
                        button.href = 'https://manage.audiencehost.com/order/config/index/hostingpkr/?group_id=3&pricing_id=6';
                        log('Set Single PKR checkout URL', 'success');
                    } else if (plan === 'business') {
                        button.href = 'https://manage.audiencehost.com/order/config/index/hostingpkr/?group_id=3&pricing_id=7';
                        log('Set Business PKR checkout URL', 'success');
                    } else if (plan === 'reseller') {
                        button.href = 'https://manage.audiencehost.com/order/config/index/hostingpkr/?group_id=3&pricing_id=8';
                        log('Set Reseller PKR checkout URL', 'success');
                    }
                    log(`PKR button final href: ${button.href}`, 'success');
                } else if (currency === 'USD') {
                    button.style.display = 'none';
                    log('Hidden USD button', 'info');
                }
            });

            updateStatus('Pakistani pricing (PKR) activated!', 'success');
            log('Pakistani pricing set successfully', 'success');
        }

        function setGlobalPricing() {
            log('Setting global USD pricing...', 'info');

            // Update checkout buttons
            const checkoutButtons = document.querySelectorAll('.checkout-btn');
            log(`Found ${checkoutButtons.length} checkout buttons for USD`, 'info');
            
            checkoutButtons.forEach((button, index) => {
                const plan = button.getAttribute('data-plan');
                const currency = button.getAttribute('data-currency');
                log(`USD Button ${index}: plan=${plan}, currency=${currency}`, 'info');

                if (currency === 'USD') {
                    button.style.display = 'inline-block';
                    // Set appropriate USD checkout URLs
                    if (plan === 'single') {
                        button.href = 'https://manage.audiencehost.com/order/config/index/hostingusd/?group_id=2&pricing_id=5';
                        log('Set Single USD checkout URL', 'success');
                    } else if (plan === 'business') {
                        button.href = 'https://manage.audiencehost.com/order/config/index/hostingusd/?group_id=2&pricing_id=6';
                        log('Set Business USD checkout URL', 'success');
                    } else if (plan === 'reseller') {
                        button.href = 'https://manage.audiencehost.com/order/config/index/hostingusd/?group_id=2&pricing_id=7';
                        log('Set Reseller USD checkout URL', 'success');
                    }
                    log(`USD button final href: ${button.href}`, 'success');
                } else if (currency === 'PKR') {
                    button.style.display = 'none';
                    log('Hidden PKR button', 'info');
                }
            });

            updateStatus('Global pricing (USD) activated!', 'info');
            log('Global USD pricing set successfully', 'success');
        }

        function testPakistaniPricing() {
            userCountry = 'PK';
            userCurrency = 'PKR';
            setPakistaniPricing();
        }

        function testGlobalPricing() {
            userCountry = 'US';
            userCurrency = 'USD';
            setGlobalPricing();
        }

        function clearLog() {
            document.getElementById('debug-log').innerHTML = '';
        }

        // Add click event listener for buttons
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('checkout-btn') || e.target.closest('.checkout-btn')) {
                const button = e.target.classList.contains('checkout-btn') ? e.target : e.target.closest('.checkout-btn');
                const plan = button.getAttribute('data-plan');
                const currency = button.getAttribute('data-currency');
                const href = button.href;

                log(`Checkout button clicked: ${plan} (${currency})`, 'success');
                log(`Button URL: ${href}`, 'info');
                
                if (href === window.location.href + '#' || href.endsWith('#')) {
                    log('ERROR: Button has no valid URL!', 'error');
                    updateStatus('Button clicked but has no valid URL!', 'error');
                    e.preventDefault(); // Prevent navigation
                    return false;
                } else {
                    log('Button has valid URL - proceeding to checkout', 'success');
                    updateStatus(`Redirecting to ${plan} checkout...`, 'success');
                }
            }
        });

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            log('Button test page loaded', 'info');
            updateStatus('Ready for testing', 'info');
            
            // Check initial button state
            setTimeout(() => {
                checkButtonHrefs();
            }, 500);
        });
    </script>
</body>
</html>
