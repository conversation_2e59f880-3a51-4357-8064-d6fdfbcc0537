<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pricing Debug Tool - AudienceHost</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .debug-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .status.success { background: #d4edda; color: #155724; }
        .status.error { background: #f8d7da; color: #721c24; }
        .status.info { background: #d1ecf1; color: #0c5460; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover { background: #0056b3; }
        .pricing-display {
            font-size: 24px;
            font-weight: bold;
            margin: 20px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
        }
        .log {
            background: #000;
            color: #0f0;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            max-height: 300px;
            overflow-y: auto;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>🔧 Pricing System Debug Tool</h1>
    
    <div class="debug-section">
        <h2>Current Status</h2>
        <div id="status-display" class="status info">Initializing...</div>
        <div id="location-info"></div>
        <div id="api-response"></div>
    </div>

    <div class="debug-section">
        <h2>Pricing Display Test</h2>
        <div class="pricing-display">
            <span class="pricing__currency" data-currency="USD">$</span>
            <span class="pricing__currency" data-currency="PKR" style="display: none;">Rs </span>
            <span class="pricing__amount" data-usd="12" data-pkr="1500">12</span>
            <span>/year</span>
        </div>
        
        <div>
            <a href="#" class="checkout-btn" data-plan="single" data-currency="USD" style="display: inline-block; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 4px; margin: 5px;">USD Checkout</a>
            <a href="#" class="checkout-btn" data-plan="single" data-currency="PKR" style="display: none; padding: 10px 20px; background: #28a745; color: white; text-decoration: none; border-radius: 4px; margin: 5px;">PKR Checkout</a>
        </div>
    </div>

    <div class="debug-section">
        <h2>Manual Tests</h2>
        <button onclick="testLocationAPI()">Test Location API</button>
        <button onclick="testPakistaniPricing()">Force Pakistani Pricing</button>
        <button onclick="testGlobalPricing()">Force Global Pricing</button>
        <button onclick="detectUserLocation()">Auto Detect Location</button>
        <button onclick="clearLog()">Clear Log</button>
    </div>

    <div class="debug-section">
        <h2>Debug Log</h2>
        <div id="debug-log" class="log"></div>
    </div>

    <script>
        let userCountry = 'US';
        let userCurrency = 'USD';
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logDiv = document.getElementById('debug-log');
            const color = type === 'error' ? '#ff6b6b' : type === 'success' ? '#51cf66' : '#74c0fc';
            logDiv.innerHTML += `<div style="color: ${color}">[${timestamp}] ${message}</div>`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(`[${timestamp}] ${message}`);
        }

        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status-display');
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
        }

        async function testLocationAPI() {
            log('Testing location API...', 'info');
            updateStatus('Testing location API...', 'info');
            
            try {
                const response = await fetch('https://ipapi.co/json/');
                const data = await response.json();
                
                log(`API Response: ${JSON.stringify(data, null, 2)}`, 'success');
                document.getElementById('api-response').innerHTML = `<pre>${JSON.stringify(data, null, 2)}</pre>`;
                document.getElementById('location-info').innerHTML = `
                    <strong>Country:</strong> ${data.country_name} (${data.country_code})<br>
                    <strong>IP:</strong> ${data.ip}<br>
                    <strong>City:</strong> ${data.city}<br>
                    <strong>Region:</strong> ${data.region}
                `;
                
                if (data.country_code === 'PK') {
                    updateStatus('Pakistani IP detected! Should show PKR pricing.', 'success');
                } else {
                    updateStatus(`Non-Pakistani IP detected (${data.country_code}). Should show USD pricing.`, 'info');
                }
                
            } catch (error) {
                log(`API Error: ${error.message}`, 'error');
                updateStatus('Location API failed!', 'error');
                document.getElementById('api-response').innerHTML = `<span style="color: red;">Error: ${error.message}</span>`;
            }
        }

        async function detectUserLocation() {
            try {
                log('Detecting user location...', 'info');
                const response = await fetch('https://ipapi.co/json/');
                const data = await response.json();

                if (data.country_code === 'PK') {
                    userCountry = 'PK';
                    userCurrency = 'PKR';
                    log('Pakistani user detected - switching to PKR', 'success');
                    setPakistaniPricing();
                } else {
                    userCountry = 'US';
                    userCurrency = 'USD';
                    log(`Global user detected (${data.country_code}) - using USD`, 'info');
                    setGlobalPricing();
                }
            } catch (error) {
                log('Location detection failed, using USD default', 'error');
                userCountry = 'US';
                userCurrency = 'USD';
                setGlobalPricing();
            }
        }

        function setPakistaniPricing() {
            log('Setting Pakistani pricing...', 'info');

            // Update Single plan pricing display
            const singleAmount = document.querySelector('[data-usd="12"]');
            const singleCurrencyUSD = document.querySelector('.pricing__currency[data-currency="USD"]');
            const singleCurrencyPKR = document.querySelector('.pricing__currency[data-currency="PKR"]');

            if (singleAmount) {
                singleAmount.textContent = '1500';
                log('Updated amount to 1500', 'success');
            } else {
                log('ERROR: Could not find amount element', 'error');
            }

            if (singleCurrencyUSD) {
                singleCurrencyUSD.style.display = 'none';
                log('Hidden USD currency symbol', 'success');
            } else {
                log('ERROR: Could not find USD currency element', 'error');
            }

            if (singleCurrencyPKR) {
                singleCurrencyPKR.style.display = 'inline';
                log('Shown PKR currency symbol', 'success');
            } else {
                log('ERROR: Could not find PKR currency element', 'error');
            }

            // Update checkout buttons
            const checkoutButtons = document.querySelectorAll('.checkout-btn');
            checkoutButtons.forEach(button => {
                const plan = button.getAttribute('data-plan');
                const currency = button.getAttribute('data-currency');

                if (plan === 'single') {
                    if (currency === 'USD') {
                        button.style.display = 'none';
                        log('Hidden USD checkout button', 'success');
                    } else if (currency === 'PKR') {
                        button.style.display = 'inline-block';
                        button.href = 'https://manage.audiencehost.com/order/config/index/hostingpkr/?group_id=3&pricing_id=6';
                        log('Shown PKR checkout button', 'success');
                    }
                }
            });

            updateStatus('Pakistani pricing (PKR) activated!', 'success');
            log('Pakistani pricing set successfully', 'success');
        }

        function setGlobalPricing() {
            log('Setting global USD pricing...', 'info');

            const amount = document.querySelector('[data-usd="12"]');
            const currencyUSD = document.querySelector('.pricing__currency[data-currency="USD"]');
            const currencyPKR = document.querySelector('.pricing__currency[data-currency="PKR"]');

            if (amount) {
                amount.textContent = '12';
                log('Updated amount to 12', 'success');
            } else {
                log('ERROR: Could not find amount element', 'error');
            }

            if (currencyUSD) {
                currencyUSD.style.display = 'inline';
                log('Shown USD currency symbol', 'success');
            } else {
                log('ERROR: Could not find USD currency element', 'error');
            }

            if (currencyPKR) {
                currencyPKR.style.display = 'none';
                log('Hidden PKR currency symbol', 'success');
            } else {
                log('ERROR: Could not find PKR currency element', 'error');
            }

            const checkoutButtons = document.querySelectorAll('.checkout-btn');
            checkoutButtons.forEach(button => {
                const currency = button.getAttribute('data-currency');
                if (currency === 'USD') {
                    button.style.display = 'inline-block';
                    button.href = 'https://manage.audiencehost.com/order/config/index/hostingusd/?group_id=2&pricing_id=5';
                    log('Shown USD checkout button', 'success');
                } else {
                    button.style.display = 'none';
                    log('Hidden PKR checkout button', 'success');
                }
            });

            updateStatus('Global pricing (USD) activated!', 'info');
            log('Global USD pricing set successfully', 'success');
        }

        function testPakistaniPricing() {
            userCountry = 'PK';
            userCurrency = 'PKR';
            setPakistaniPricing();
        }

        function testGlobalPricing() {
            userCountry = 'US';
            userCurrency = 'USD';
            setGlobalPricing();
        }

        function clearLog() {
            document.getElementById('debug-log').innerHTML = '';
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            log('Debug page loaded', 'info');
            updateStatus('Ready for testing', 'info');
            
            // Auto-test the location API
            setTimeout(() => {
                testLocationAPI();
                setTimeout(() => {
                    detectUserLocation();
                }, 1000);
            }, 500);
        });
    </script>
</body>
</html>
