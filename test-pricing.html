<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Pricing System</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ccc; }
        button { padding: 10px 20px; margin: 10px; background: #007cba; color: white; border: none; cursor: pointer; }
        .pricing-display { font-size: 24px; margin: 10px 0; }
        .checkout-btn { display: inline-block; padding: 10px 20px; background: #28a745; color: white; text-decoration: none; margin: 5px; }
    </style>
</head>
<body>
    <h1>Pricing System Test</h1>
    
    <div class="test-section">
        <h2>Current Pricing Display</h2>
        <div class="pricing-display">
            <span class="pricing__currency" data-currency="USD">$</span>
            <span class="pricing__currency" data-currency="PKR" style="display: none;">Rs </span>
            <span class="pricing__amount" data-usd="12" data-pkr="1500">12</span>
            <span>/year</span>
        </div>
        
        <div>
            <a href="#" class="checkout-btn" data-plan="single" data-currency="USD">USD Checkout</a>
            <a href="#" class="checkout-btn" data-plan="single" data-currency="PKR" style="display: none;">PKR Checkout</a>
        </div>
    </div>
    
    <div class="test-section">
        <h2>Manual Testing</h2>
        <button onclick="testPakistaniPricing()">Test Pakistani Pricing (PKR)</button>
        <button onclick="testGlobalPricing()">Test Global Pricing (USD)</button>
        <button onclick="detectUserLocation()">Auto Detect Location</button>
    </div>
    
    <div class="test-section">
        <h2>Console Output</h2>
        <p>Open browser console (F12) to see detailed logs</p>
    </div>

    <script>
        // Copy the pricing functions from main.js
        let userCountry = 'US';
        let userCurrency = 'USD';

        async function detectUserLocation() {
            try {
                console.log('Detecting user location...');
                const response = await fetch('https://ipapi.co/json/');
                const data = await response.json();
                
                if (data.country_code === 'PK') {
                    userCountry = 'PK';
                    userCurrency = 'PKR';
                    console.log('Pakistani user detected - switching to PKR');
                    setPakistaniPricing();
                } else {
                    userCountry = 'US';
                    userCurrency = 'USD';
                    console.log('Global user detected - using USD');
                    setGlobalPricing();
                }
            } catch (error) {
                console.log('Location detection failed, using USD default');
                userCountry = 'US';
                userCurrency = 'USD';
                setGlobalPricing();
            }
        }

        function setPakistaniPricing() {
            console.log('Setting Pakistani pricing...');
            
            const amount = document.querySelector('[data-usd="12"]');
            const currencyUSD = document.querySelector('.pricing__currency[data-currency="USD"]');
            const currencyPKR = document.querySelector('.pricing__currency[data-currency="PKR"]');
            const checkoutButtons = document.querySelectorAll('.checkout-btn');
            
            if (amount) amount.textContent = '1500';
            if (currencyUSD) currencyUSD.style.display = 'none';
            if (currencyPKR) currencyPKR.style.display = 'inline';
            
            checkoutButtons.forEach(button => {
                const currency = button.getAttribute('data-currency');
                if (currency === 'PKR') {
                    button.style.display = 'inline-block';
                    button.href = 'https://manage.audiencehost.com/order/config/index/hosting/?group_id=2&pricing_id=4';
                } else {
                    button.style.display = 'none';
                }
            });
            
            console.log('Pakistani pricing set successfully');
        }

        function setGlobalPricing() {
            console.log('Setting global USD pricing...');
            
            const amount = document.querySelector('[data-usd="12"]');
            const currencyUSD = document.querySelector('.pricing__currency[data-currency="USD"]');
            const currencyPKR = document.querySelector('.pricing__currency[data-currency="PKR"]');
            const checkoutButtons = document.querySelectorAll('.checkout-btn');
            
            if (amount) amount.textContent = '12';
            if (currencyUSD) currencyUSD.style.display = 'inline';
            if (currencyPKR) currencyPKR.style.display = 'none';
            
            checkoutButtons.forEach(button => {
                const currency = button.getAttribute('data-currency');
                if (currency === 'USD') {
                    button.style.display = 'inline-block';
                    button.href = 'https://manage.audiencehost.com/order/config/index/hostingusd/?group_id=2&pricing_id=5';
                } else {
                    button.style.display = 'none';
                }
            });
            
            console.log('Global USD pricing set successfully');
        }

        function testPakistaniPricing() {
            userCountry = 'PK';
            userCurrency = 'PKR';
            setPakistaniPricing();
        }

        function testGlobalPricing() {
            userCountry = 'US';
            userCurrency = 'USD';
            setGlobalPricing();
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Test page loaded');
            detectUserLocation();
        });
    </script>
</body>
</html>
