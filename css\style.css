/* ===== RESET & BASE STYLES ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: 16px;
    font-weight: 400;
    line-height: 1.6;
    color: #334155;
    background-color: #ffffff;
    overflow-x: hidden;
}

/* ===== UTILITY CLASSES ===== */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    font-size: 0.95rem;
    font-weight: 500;
    text-decoration: none;
    border-radius: 8px;
    border: 2px solid transparent;
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap;
}

.btn--primary {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: white;
    border-color: #3b82f6;
}

.btn--primary:hover {
    background: linear-gradient(135deg, #1d4ed8, #1e40af);
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(59, 130, 246, 0.3);
}

.btn--outline {
    background: transparent;
    color: #3b82f6;
    border-color: #3b82f6;
}

.btn--outline:hover {
    background: #3b82f6;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(59, 130, 246, 0.2);
}

.btn--ghost {
    background: transparent;
    color: #64748b;
    border-color: #e2e8f0;
}

.btn--ghost:hover {
    background: #f8fafc;
    color: #334155;
    border-color: #cbd5e1;
}

.btn--full {
    width: 100%;
    justify-content: center;
}

.section__header {
    text-align: center;
    margin-bottom: 4rem;
}

.section__title {
    font-size: 2.5rem;
    font-weight: 700;
    color: #1e293b;
    margin-bottom: 1rem;
    line-height: 1.2;
}

.section__subtitle {
    font-size: 1.125rem;
    color: #64748b;
    max-width: 600px;
    margin: 0 auto;
}

/* ===== HEADER ===== */
.header {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid #e2e8f0;
    z-index: 1000;
    transition: all 0.3s ease;
}

.nav {
    height: 70px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav__data {
    display: flex;
    align-items: center;
}

.nav__logo {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    text-decoration: none;
    color: #1e293b;
    font-weight: 700;
    font-size: 1.25rem;
}

.nav__logo-icon {
    font-size: 1.5rem;
    color: #3b82f6;
}

.nav__toggle {
    display: none;
    font-size: 1.25rem;
    color: #64748b;
    cursor: pointer;
}

.nav__menu {
    display: flex;
    align-items: center;
    gap: 2rem;
}

.nav__list {
    display: flex;
    list-style: none;
    gap: 2rem;
    margin: 0;
}

.nav__link {
    color: #64748b;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
}

.nav__link:hover {
    color: #3b82f6;
}

.nav__actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.nav__close {
    display: none;
}

.client-login-btn {
    font-size: 0.9rem;
    padding: 0.6rem 1.2rem;
}

/* ===== HERO SECTION ===== */
.hero {
    padding: 120px 0 80px;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    position: relative;
    overflow: hidden;
}

.hero__container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.hero__title {
    font-size: 3.5rem;
    font-weight: 800;
    color: #1e293b;
    line-height: 1.1;
    margin-bottom: 1.5rem;
}

.hero__title-accent {
    color: #3b82f6;
}

.hero__description {
    font-size: 1.25rem;
    color: #64748b;
    margin-bottom: 2rem;
    line-height: 1.6;
}

.hero__features {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    margin-bottom: 2.5rem;
}

.hero__feature {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    color: #475569;
    font-weight: 500;
}

.hero__feature i {
    color: #10b981;
    font-size: 1.1rem;
}

.hero__actions {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.hero__image {
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
}

.hero__servers {
    position: relative;
    width: 300px;
    height: 300px;
}

.server {
    position: absolute;
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 2rem;
    box-shadow: 0 10px 30px rgba(59, 130, 246, 0.3);
    animation: float 6s ease-in-out infinite;
}

.server--1 {
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    animation-delay: 0s;
}

.server--2 {
    bottom: 20px;
    left: 0;
    animation-delay: 2s;
}

.server--3 {
    bottom: 20px;
    right: 0;
    animation-delay: 4s;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) translateX(-50%); }
    50% { transform: translateY(-20px) translateX(-50%); }
}

.server--2 {
    animation-name: floatLeft;
}

.server--3 {
    animation-name: floatRight;
}

@keyframes floatLeft {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-15px); }
}

@keyframes floatRight {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-25px); }
}

/* ===== PRICING SECTION ===== */
.pricing {
    padding: 80px 0;
    background: #ffffff;
}

.currency__toggle {
    display: flex;
    justify-content: center;
    margin-bottom: 3rem;
}

.currency__info {
    background: #f8fafc;
    padding: 1rem 2rem;
    border-radius: 12px;
    border: 1px solid #e2e8f0;
    text-align: center;
}

.currency__label {
    font-weight: 500;
    color: #64748b;
    margin-right: 0.5rem;
}

.currency__current {
    font-weight: 600;
    color: #3b82f6;
}

.currency__auto {
    display: block;
    color: #94a3b8;
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

.pricing__grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    max-width: 1000px;
    margin: 0 auto;
}

.pricing__card {
    background: #ffffff;
    border: 2px solid #e2e8f0;
    border-radius: 16px;
    padding: 2rem;
    position: relative;
    transition: all 0.3s ease;
}

.pricing__card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    border-color: #3b82f6;
}

.pricing__card--featured {
    border-color: #3b82f6;
    background: linear-gradient(135deg, #f8fafc, #ffffff);
}

.pricing__badge {
    position: absolute;
    top: -12px;
    left: 50%;
    transform: translateX(-50%);
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: white;
    padding: 0.5rem 1.5rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 600;
}

.pricing__header {
    text-align: center;
    margin-bottom: 1.5rem;
}

.pricing__title {
    font-size: 1.5rem;
    font-weight: 700;
    color: #1e293b;
    margin-bottom: 0.5rem;
}

.pricing__subtitle {
    color: #64748b;
    font-size: 0.95rem;
}

.pricing__price {
    text-align: center;
    margin-bottom: 2rem;
}

.pricing__currency {
    font-size: 1.5rem;
    font-weight: 600;
    color: #3b82f6;
}

.pricing__amount {
    font-size: 3rem;
    font-weight: 800;
    color: #1e293b;
}

.pricing__period {
    color: #64748b;
    font-size: 1rem;
}

.pricing__features {
    margin-bottom: 2rem;
}

.pricing__feature {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 0;
    border-bottom: 1px solid #f1f5f9;
}

.pricing__feature:last-child {
    border-bottom: none;
}

.pricing__feature i {
    color: #10b981;
    font-size: 1rem;
    width: 16px;
}

.pricing__feature span {
    color: #475569;
    font-weight: 500;
}

/* ===== FEATURES SECTION ===== */
.features {
    padding: 80px 0;
    background: #f8fafc;
}

.features__grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.feature__card {
    background: #ffffff;
    padding: 2rem;
    border-radius: 16px;
    text-align: center;
    border: 1px solid #e2e8f0;
    transition: all 0.3s ease;
}

.feature__card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.feature__icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    color: white;
    font-size: 2rem;
}

.feature__title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 1rem;
}

.feature__description {
    color: #64748b;
    line-height: 1.6;
}

/* ===== TESTIMONIALS SECTION ===== */
.testimonials {
    padding: 80px 0;
    background: #ffffff;
}

.testimonials__grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.testimonial__card {
    background: #f8fafc;
    padding: 2rem;
    border-radius: 16px;
    border: 1px solid #e2e8f0;
    transition: all 0.3s ease;
}

.testimonial__card:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.testimonial__rating {
    display: flex;
    gap: 0.25rem;
    margin-bottom: 1rem;
}

.testimonial__rating i {
    color: #fbbf24;
    font-size: 1.1rem;
}

.testimonial__text {
    color: #475569;
    line-height: 1.6;
    margin-bottom: 1.5rem;
    font-style: italic;
}

.testimonial__author {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.testimonial__avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    object-fit: cover;
}

.testimonial__name {
    font-size: 1rem;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 0.25rem;
}

.testimonial__role {
    font-size: 0.875rem;
    color: #64748b;
}

/* ===== FOOTER ===== */
.footer {
    background: #1e293b;
    color: #cbd5e1;
    padding: 60px 0 30px;
}

.footer__content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 3rem;
    margin-bottom: 3rem;
}

.footer__logo {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-size: 1.25rem;
    font-weight: 700;
    color: #ffffff;
    margin-bottom: 1rem;
}

.footer__logo i {
    color: #3b82f6;
    font-size: 1.5rem;
}

.footer__description {
    color: #94a3b8;
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.footer__social {
    display: flex;
    gap: 1rem;
}

.footer__social-link {
    width: 40px;
    height: 40px;
    background: #334155;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #cbd5e1;
    text-decoration: none;
    transition: all 0.3s ease;
}

.footer__social-link:hover {
    background: #3b82f6;
    color: white;
    transform: translateY(-2px);
}

.footer__title {
    font-size: 1.125rem;
    font-weight: 600;
    color: #ffffff;
    margin-bottom: 1.5rem;
}

.footer__links {
    list-style: none;
}

.footer__links li {
    margin-bottom: 0.75rem;
}

.footer__link {
    color: #94a3b8;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer__link:hover {
    color: #3b82f6;
}

.footer__contact-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 1rem;
    color: #94a3b8;
}

.footer__contact-item i {
    color: #3b82f6;
    width: 16px;
}

.footer__bottom {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 2rem;
    border-top: 1px solid #334155;
    flex-wrap: wrap;
    gap: 1rem;
}

.footer__copyright {
    color: #94a3b8;
}

.footer__legal {
    display: flex;
    gap: 2rem;
}

.footer__legal-link {
    color: #94a3b8;
    text-decoration: none;
    font-size: 0.875rem;
    transition: color 0.3s ease;
}

.footer__legal-link:hover {
    color: #3b82f6;
}

/* ===== RESPONSIVE DESIGN ===== */
@media screen and (max-width: 1024px) {
    .hero__container {
        grid-template-columns: 1fr;
        gap: 3rem;
        text-align: center;
    }
    
    .hero__title {
        font-size: 3rem;
    }
    
    .pricing__grid {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    }
    
    .features__grid {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    }
}

@media screen and (max-width: 768px) {
    .container {
        padding: 0 1.5rem;
    }
    
    .nav__menu {
        position: fixed;
        top: 0;
        right: -100%;
        width: 100%;
        height: 100vh;
        background: rgba(255, 255, 255, 0.98);
        backdrop-filter: blur(10px);
        flex-direction: column;
        justify-content: center;
        transition: right 0.3s ease;
        z-index: 1000;
    }
    
    .nav__menu.show-menu {
        right: 0;
    }
    
    .nav__list {
        flex-direction: column;
        gap: 2rem;
        text-align: center;
    }
    
    .nav__link {
        font-size: 1.125rem;
    }
    
    .nav__actions {
        margin-top: 2rem;
    }
    
    .nav__toggle {
        display: block;
    }
    
    .nav__close {
        display: block;
        position: absolute;
        top: 2rem;
        right: 2rem;
        font-size: 1.5rem;
        color: #64748b;
        cursor: pointer;
    }
    
    .hero {
        padding: 100px 0 60px;
    }
    
    .hero__title {
        font-size: 2.5rem;
    }
    
    .hero__actions {
        justify-content: center;
    }
    
    .section__title {
        font-size: 2rem;
    }
    
    .pricing__grid {
        grid-template-columns: 1fr;
        max-width: 400px;
    }
    
    .features__grid {
        grid-template-columns: 1fr;
    }
    
    .testimonials__grid {
        grid-template-columns: 1fr;
    }
    
    .footer__content {
        grid-template-columns: 1fr;
        gap: 2rem;
        text-align: center;
    }
    
    .footer__bottom {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }
}

@media screen and (max-width: 480px) {
    .hero__title {
        font-size: 2rem;
    }
    
    .hero__description {
        font-size: 1.1rem;
    }
    
    .section__title {
        font-size: 1.75rem;
    }
    
    .pricing__card {
        padding: 1.5rem;
    }
    
    .feature__card {
        padding: 1.5rem;
    }
    
    .testimonial__card {
        padding: 1.5rem;
    }
}

/* ===== LOADING OPTIMIZATIONS ===== */
.hero__servers,
.feature__icon,
.testimonial__avatar {
    will-change: transform;
}

/* Reduce motion for users who prefer it */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
    
    .server {
        animation: none;
    }
}

/* Print styles */
@media print {
    .header,
    .nav__toggle,
    .hero__image,
    .footer__social {
        display: none;
    }
    
    .hero {
        padding: 2rem 0;
    }
    
    body {
        font-size: 12pt;
        line-height: 1.4;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .btn--primary {
        background: #000000;
        border-color: #000000;
    }
    
    .btn--outline {
        border-color: #000000;
        color: #000000;
    }
}

/* Focus styles for accessibility */
.btn:focus,
.nav__link:focus,
.footer__link:focus {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
}