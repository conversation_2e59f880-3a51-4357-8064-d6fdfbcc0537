/* ===== AUDIENCE HOST - MAIN JAVASCRIPT ===== */

// ===== GLOBAL VARIABLES =====
let userCountry = 'US';
let userCurrency = 'USD';
let exchangeRate = 1;

// ===== DOM ELEMENTS =====
const navToggle = document.getElementById('nav-toggle');
const navMenu = document.getElementById('nav-menu');
const navClose = document.getElementById('nav-close');
const header = document.getElementById('header');

// ===== INITIALIZATION =====
document.addEventListener('DOMContentLoaded', function() {
    initializeNavigation();
    detectUserLocation();
    initializeScrollEffects();
    initializeSmoothScrolling();
    optimizePerformance();
});

// ===== NAVIGATION FUNCTIONS =====
function initializeNavigation() {
    // Mobile menu toggle
    if (navToggle) {
        navToggle.addEventListener('click', () => {
            navMenu.classList.add('show-menu');
        });
    }

    if (navClose) {
        navClose.addEventListener('click', () => {
            navMenu.classList.remove('show-menu');
        });
    }

    // Close menu when clicking on nav links
    const navLinks = document.querySelectorAll('.nav__link');
    navLinks.forEach(link => {
        link.addEventListener('click', () => {
            navMenu.classList.remove('show-menu');
        });
    });

    // Close menu when clicking outside
    document.addEventListener('click', (e) => {
        if (!navMenu.contains(e.target) && !navToggle.contains(e.target)) {
            navMenu.classList.remove('show-menu');
        }
    });
}

// ===== SCROLL EFFECTS =====
function initializeScrollEffects() {
    window.addEventListener('scroll', () => {
        // Header background change on scroll
        if (header) {
            if (window.scrollY >= 50) {
                header.style.background = 'rgba(255, 255, 255, 0.98)';
                header.style.boxShadow = '0 2px 20px rgba(0, 0, 0, 0.1)';
            } else {
                header.style.background = 'rgba(255, 255, 255, 0.95)';
                header.style.boxShadow = 'none';
            }
        }

        // Animate elements on scroll
        animateOnScroll();
    });
}

function animateOnScroll() {
    const elements = document.querySelectorAll('.pricing__card, .feature__card, .testimonial__card');
    
    elements.forEach(element => {
        const elementTop = element.getBoundingClientRect().top;
        const elementVisible = 150;
        
        if (elementTop < window.innerHeight - elementVisible) {
            element.style.opacity = '1';
            element.style.transform = 'translateY(0)';
        }
    });
}

// ===== SMOOTH SCROLLING =====
function initializeSmoothScrolling() {
    const links = document.querySelectorAll('a[href^="#"]');
    
    links.forEach(link => {
        link.addEventListener('click', (e) => {
            e.preventDefault();
            
            const targetId = link.getAttribute('href');
            const targetSection = document.querySelector(targetId);
            
            if (targetSection) {
                const headerHeight = header ? header.offsetHeight : 70;
                const targetPosition = targetSection.offsetTop - headerHeight;
                
                window.scrollTo({
                    top: targetPosition,
                    behavior: 'smooth'
                });
            }
        });
    });
}

// ===== LOCATION DETECTION & PRICING =====
async function detectUserLocation() {
    try {
        // Try to get user's country via IP geolocation
        const response = await fetch('https://ipapi.co/json/', {
            timeout: 5000
        });
        
        if (response.ok) {
            const data = await response.json();
            userCountry = data.country_code || 'US';
        }
    } catch (error) {
        console.log('Geolocation detection failed, using default USD pricing');
    }
    
    // Update pricing based on detected location
    updatePricingCurrency();
}

function updatePricingCurrency() {
    const currentCurrencyEl = document.getElementById('current-currency');
    const pricingAmounts = document.querySelectorAll('.pricing__amount');
    const currencySymbols = document.querySelectorAll('.pricing__currency');
    const checkoutButtons = document.querySelectorAll('.checkout-btn');
    
    // Determine currency based on country
    if (userCountry === 'PK') {
        userCurrency = 'PKR';
        
        // Update currency display
        if (currentCurrencyEl) {
            currentCurrencyEl.textContent = 'PKR';
        }
        
        // Show PKR prices and hide USD
        currencySymbols.forEach(symbol => {
            if (symbol.getAttribute('data-currency') === 'PKR') {
                symbol.style.display = 'inline';
            } else {
                symbol.style.display = 'none';
            }
        });
        
        // Update pricing amounts
        pricingAmounts.forEach(amount => {
            const pkrPrice = amount.getAttribute('data-pkr');
            if (pkrPrice) {
                amount.textContent = pkrPrice;
            }
        });
        
        // Show appropriate checkout buttons
        checkoutButtons.forEach(button => {
            if (button.getAttribute('data-currency') === 'PKR') {
                button.style.display = 'flex';
                button.href = getCheckoutUrl(button.getAttribute('data-plan'), 'PKR');
            } else {
                button.style.display = 'none';
            }
        });
        
    } else {
        userCurrency = 'USD';
        
        // Update currency display
        if (currentCurrencyEl) {
            currentCurrencyEl.textContent = 'USD';
        }
        
        // Show USD prices and hide PKR
        currencySymbols.forEach(symbol => {
            if (symbol.getAttribute('data-currency') === 'USD') {
                symbol.style.display = 'inline';
            } else {
                symbol.style.display = 'none';
            }
        });
        
        // Update pricing amounts
        pricingAmounts.forEach(amount => {
            const usdPrice = amount.getAttribute('data-usd');
            if (usdPrice) {
                amount.textContent = usdPrice;
            }
        });
        
        // Show appropriate checkout buttons
        checkoutButtons.forEach(button => {
            if (button.getAttribute('data-currency') === 'USD') {
                button.style.display = 'flex';
                button.href = getCheckoutUrl(button.getAttribute('data-plan'), 'USD');
            } else {
                button.style.display = 'none';
            }
        });
    }
    
    // Add animation class
    document.querySelector('.pricing')?.classList.add('currency-updated');
}

function getCheckoutUrl(plan, currency) {
    // Generate checkout URLs based on plan and currency
    const baseUrl = 'https://billing.audiencehost.com/checkout';
    const planUrls = {
        'USD': {
            'single': `${baseUrl}/single-usd?plan=single&currency=usd&price=12&billing=yearly`,
            'business': `${baseUrl}/business-usd?plan=business&currency=usd&price=25&billing=yearly`,
            'reseller': `${baseUrl}/reseller-usd?plan=reseller&currency=usd&price=50&billing=yearly`
        },
        'PKR': {
            'single': `${baseUrl}/single-pkr?plan=single&currency=pkr&price=2400&billing=yearly`,
            'business': `${baseUrl}/business-pkr?plan=business&currency=pkr&price=5000&billing=yearly`,
            'reseller': `${baseUrl}/reseller-pkr?plan=reseller&currency=pkr&price=10000&billing=yearly`
        }
    };
    
    return planUrls[currency][plan] || '#';
}

// ===== CLIENT LOGIN FUNCTIONALITY =====
function initializeClientLogin() {
    const clientLoginBtns = document.querySelectorAll('.client-login-btn');
    
    clientLoginBtns.forEach(btn => {
        btn.addEventListener('click', (e) => {
            e.preventDefault();
            
            // Redirect to client portal
            window.open('https://portal.audiencehost.com/login', '_blank');
        });
    });
}

// ===== PERFORMANCE OPTIMIZATIONS =====
function optimizePerformance() {
    // Lazy load images
    const images = document.querySelectorAll('img[data-src]');
    const imageObserver = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const img = entry.target;
                img.src = img.dataset.src;
                img.classList.remove('lazy');
                observer.unobserve(img);
            }
        });
    });
    
    images.forEach(img => imageObserver.observe(img));
    
    // Preload critical resources
    preloadCriticalResources();
    
    // Initialize client login after DOM is ready
    initializeClientLogin();
}

function preloadCriticalResources() {
    // Preload fonts
    const fontLink = document.createElement('link');
    fontLink.rel = 'preload';
    fontLink.href = 'https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap';
    fontLink.as = 'style';
    document.head.appendChild(fontLink);
    
    // Preload critical images
    const criticalImages = [
        'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=60&h=60&fit=crop&crop=face',
        'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=60&h=60&fit=crop&crop=face',
        'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=60&h=60&fit=crop&crop=face'
    ];
    
    criticalImages.forEach(src => {
        const img = new Image();
        img.src = src;
    });
}

// ===== FORM VALIDATION & ANALYTICS =====
function trackEvent(eventName, eventData = {}) {
    // Analytics tracking (replace with your analytics provider)
    try {
        if (typeof gtag !== 'undefined') {
            gtag('event', eventName, eventData);
        }
        
        // Custom analytics
        console.log(`Event tracked: ${eventName}`, eventData);
    } catch (error) {
        console.warn('Analytics tracking failed:', error);
    }
}

// ===== PRICING PLAN INTERACTIONS =====
document.addEventListener('click', (e) => {
    if (e.target.classList.contains('checkout-btn')) {
        const plan = e.target.getAttribute('data-plan');
        const currency = e.target.getAttribute('data-currency');
        
        trackEvent('checkout_click', {
            plan: plan,
            currency: currency,
            user_country: userCountry
        });
    }
});

// ===== SEO & PERFORMANCE MONITORING =====
function initializeSEOOptimizations() {
    // Add structured data for better SEO
    const structuredData = {
        "@context": "https://schema.org",
        "@type": "Product",
        "name": "Cheap Reseller Hosting",
        "description": "Professional reseller hosting and web hosting plans with 99.9% uptime guarantee",
        "brand": {
            "@type": "Brand",
            "name": "Audience Host"
        },
        "offers": [
            {
                "@type": "Offer",
                "name": "Single Hosting Plan",
                "price": "1.99",
                "priceCurrency": "USD",
                "availability": "https://schema.org/InStock"
            },
            {
                "@type": "Offer",
                "name": "Business Hosting Plan", 
                "price": "4.99",
                "priceCurrency": "USD",
                "availability": "https://schema.org/InStock"
            },
            {
                "@type": "Offer",
                "name": "Agency/Reseller Hosting Plan",
                "price": "9.99", 
                "priceCurrency": "USD",
                "availability": "https://schema.org/InStock"
            }
        ]
    };
    
    // Add to page if not already present
    if (!document.querySelector('script[type="application/ld+json"][data-product]')) {
        const script = document.createElement('script');
        script.type = 'application/ld+json';
        script.setAttribute('data-product', 'true');
        script.textContent = JSON.stringify(structuredData);
        document.head.appendChild(script);
    }
}

// ===== ACCESSIBILITY ENHANCEMENTS =====
function initializeAccessibility() {
    // Add keyboard navigation support
    const focusableElements = document.querySelectorAll(
        'a, button, input, textarea, select, [tabindex]:not([tabindex="-1"])'
    );
    
    // Handle keyboard navigation
    document.addEventListener('keydown', (e) => {
        if (e.key === 'Escape') {
            navMenu.classList.remove('show-menu');
        }
        
        // Enhanced focus management
        if (e.key === 'Tab') {
            const activeElement = document.activeElement;
            const focusableArray = Array.from(focusableElements);
            const currentIndex = focusableArray.indexOf(activeElement);
            
            if (e.shiftKey) {
                // Shift + Tab (backwards)
                if (currentIndex <= 0) {
                    e.preventDefault();
                    focusableArray[focusableArray.length - 1].focus();
                }
            } else {
                // Tab (forwards)
                if (currentIndex >= focusableArray.length - 1) {
                    e.preventDefault();
                    focusableArray[0].focus();
                }
            }
        }
    });
    
    // Add aria labels dynamically
    const buttons = document.querySelectorAll('button, .btn');
    buttons.forEach(button => {
        if (!button.getAttribute('aria-label') && !button.textContent.trim()) {
            const icon = button.querySelector('i');
            if (icon) {
                const iconClass = icon.className;
                if (iconClass.includes('fa-bars')) {
                    button.setAttribute('aria-label', 'Open navigation menu');
                } else if (iconClass.includes('fa-times')) {
                    button.setAttribute('aria-label', 'Close navigation menu');
                }
            }
        }
    });
}

// ===== ERROR HANDLING =====
window.addEventListener('error', (e) => {
    console.warn('JavaScript error caught:', e.error);
    
    // Graceful degradation for critical features
    if (e.error && e.error.message.includes('fetch')) {
        console.log('Network error detected, using default USD pricing');
        userCountry = 'US';
        userCurrency = 'USD';
        updatePricingCurrency();
    }
});

// ===== FINAL INITIALIZATION =====
document.addEventListener('DOMContentLoaded', () => {
    initializeSEOOptimizations();
    initializeAccessibility();
    
    // Add loading complete class
    document.body.classList.add('loaded');
    
    console.log('🚀 Audience Host website fully loaded and optimized!');
});

// ===== WEB VITALS MONITORING =====
function initializeWebVitals() {
    // Monitor Core Web Vitals for performance optimization
    if ('PerformanceObserver' in window) {
        // Largest Contentful Paint
        new PerformanceObserver((entryList) => {
            const entries = entryList.getEntries();
            const lastEntry = entries[entries.length - 1];
            console.log('LCP:', lastEntry.startTime);
        }).observe({ entryTypes: ['largest-contentful-paint'] });
        
        // First Input Delay
        new PerformanceObserver((entryList) => {
            const entries = entryList.getEntries();
            entries.forEach(entry => {
                console.log('FID:', entry.processingStart - entry.startTime);
            });
        }).observe({ entryTypes: ['first-input'] });
        
        // Cumulative Layout Shift
        new PerformanceObserver((entryList) => {
            let cls = 0;
            for (const entry of entryList.getEntries()) {
                if (!entry.hadRecentInput) {
                    cls += entry.value;
                }
            }
            console.log('CLS:', cls);
        }).observe({ entryTypes: ['layout-shift'] });
    }
}

// Initialize Web Vitals monitoring
initializeWebVitals();

// ===== EXPORT FUNCTIONS FOR EXTERNAL USE =====
window.AudienceHost = {
    updatePricing: updatePricingCurrency,
    trackEvent: trackEvent,
    userCountry: () => userCountry,
    userCurrency: () => userCurrency
};